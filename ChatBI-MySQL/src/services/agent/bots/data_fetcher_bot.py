"""
Data fetcher bot implementation.
"""

import yaml
import textwrap
import json # Added import
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Callable

from agents import Agent, Model, ModelSettings

from src.models.user_info_class import UserInfo
from src.services.agent.bots.base_bot import BaseBot
from src.services.agent.utils.model_provider import (
    LITE_LLM_MODEL,
    OPENAI_MODEL_SETTINGS,
    CACHE_ENABLED_CLAUDE_MODEL,
    get_model_for_name
)
from src.services.agent.utils.permissions import get_user_permission
from src.services.agent.tools.tool_manager import tool_manager
from src.utils.resource_manager import load_resource
from src.utils.logger import logger


def _load_config(config_file: str) -> Dict[str, Any]:
    """
    Load the agent configuration from a YAML file.

    Args:
        config_file: Name of the YAML configuration file

    Returns:
        Dict containing the configuration
    """
    # 使用 resource_manager 加载配置文件
    yaml_content = load_resource("data_fetcher_bot_config", config_file)

    if yaml_content:
        try:
            return yaml.safe_load(yaml_content)
        except Exception as e:
            # 如果 YAML 解析失败，记录错误并返回默认配置
            logger.error(f"Error parsing YAML config file {config_file}: {e}")
    else:
        logger.error(
            f"Config file {config_file} not found in data_fetcher_bot_config directory"
        )

    # 如果加载或解析失败，返回默认配置
    return {
        "agent_name": "sales_order_analytics",
        "agent_description": "销售订单分析专家",
        "agent_tables": [],
    }


class DataFetcherBot(BaseBot):
    """
    Data fetcher bot for retrieving data via SQL queries.

    This bot is specialized in:
    - Fetching DDL information for specific tables
    - Writing and executing SQL queries
    - Retrieving sample data from tables

    The bot can be configured with different YAML files to specialize in different domains.
    """

    def __init__(
        self, user_info: Dict[str, Any], config_file: str = "sales_orders.yml"
    ):
        super().__init__(user_info)
        self.config_file = config_file
        self.config = _load_config(config_file)
        self.table_with_desc = [
            f"- {t.get('name', 'N/A')}: {t.get('desc', 'N/A')}"
            for t in self.config.get("agent_tables", [])
        ]
        self.table_with_desc = "\n".join(self.table_with_desc)

    def get_description(self) -> str:
        agent_name = self.config.get("agent_name", "特定领域")
        agent_description = self.config.get("agent_description", "未提供描述")

        return f"我是一个专注于{agent_name}分析的专家，负责业务:\n{agent_description}\n我可以处理以下相关数据表: {self.table_with_desc}"

    def create_agent(self, model: Optional[Model] = None, model_settings_str: Optional[str] = None) -> Agent:
        agent_name = self.config.get("agent_name", "特定领域")
        agent_description = self.config.get("agent_description", "")
        tools = self.config.get("tools", [])

        # 从配置文件读取model配置，如果没有则使用传入的参数或默认值
        config_model = self.config.get("model")
        config_model_settings = self.config.get("model_settings")

        logger.info(f"agent_name:{agent_name}, tools:{tools}, config_model:{config_model}, config_model_settings:{config_model_settings}")
        tool_list = tool_manager.get_tool_list([tool['name'] for tool in tools])

        system_instruction = load_resource("prompt", "data_fetcher_instruction.md")
        domain_instruction = textwrap.dedent(
            f"""
            你是一个专注于 {agent_name} 分析的专家。
            核心业务:
            {agent_description}
            相关数据表:
            {self.table_with_desc}
        """
        ).strip()

        # Create realtime instruction with user context
        realtime_instruction = self.get_user_realtime_instruction()

        instruction = (
            f"{system_instruction}" f"{domain_instruction}" f"{realtime_instruction}"
        )

        # 确定使用的模型
        final_model = model
        if config_model:
            # 从配置文件中读取模型名称，创建对应的模型实例
            try:
                final_model = get_model_for_name(config_model)
                logger.info(f"使用配置文件中的模型: {config_model}")
            except Exception as e:
                logger.error(f"创建配置模型失败: {e}，使用默认模型")
                final_model = CACHE_ENABLED_CLAUDE_MODEL
        elif final_model is None:
            # 如果没有传入模型且配置文件中也没有，使用默认模型
            final_model = CACHE_ENABLED_CLAUDE_MODEL

        # 处理model_settings
        model_settings = None
        settings_source = config_model_settings or model_settings_str or OPENAI_MODEL_SETTINGS

        if settings_source and settings_source != "" and settings_source != "{}":
            try:
                if isinstance(settings_source, dict):
                    # 如果配置文件中的model_settings是字典格式
                    model_settings = ModelSettings(**settings_source)
                    logger.info(f"使用配置文件中的model_settings: {settings_source}")
                else:
                    # 如果是JSON字符串格式
                    model_settings_dict = json.loads(settings_source)
                    model_settings = ModelSettings(**model_settings_dict)
                    logger.info(f"解析model_settings成功: {model_settings_dict}")
            except json.JSONDecodeError as e:
                logger.error(f"解析model_settings JSON失败: {e}")
            except TypeError as e:
                logger.error(f"创建ModelSettings失败: {e}")

        # 创建Agent
        agent_kwargs = {
            "name": f"{agent_name}_specialist",
            "instructions": instruction,
            "model": final_model,
            "tools": tool_list
        }

        if model_settings is not None:
            agent_kwargs["model_settings"] = model_settings

        agent = Agent[UserInfo](**agent_kwargs)
        return agent
